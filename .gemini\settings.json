{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "nova-memory": {"type": "stdio", "command": "npx", "args": ["@nova-mcp/mcp-nova"], "timeout": 30000, "enabled": true, "env": {"NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data", "NOVA_LOG_LEVEL": "info"}, "autoapprove": ["memory", "board", "workflow", "quick", "relationships", "analysis", "project", "settings", "help"]}}}