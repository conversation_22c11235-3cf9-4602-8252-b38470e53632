// Google Generative AI (Gemini) 示例脚本
// 使用前需要设置环境变量 GEMINI_API_KEY

const { GoogleGenerativeAI } = require('@google/generative-ai');

// 初始化 Gemini AI
// 需要从 Google AI Studio 获取 API Key: https://makersuite.google.com/app/apikey
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || 'YOUR_API_KEY_HERE');

async function testGemini() {
  try {
    // 使用 Gemini Pro 模型
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

    const prompt = '请用中文介绍一下 Node.js 的主要特点';

    console.log('🤖 发送提问:', prompt);
    console.log('⏳ 等待 Gemini 响应...\n');

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    console.log('✨ Gemini 回复:');
    console.log(text);
  } catch (error) {
    console.error('❌ 发生错误:', error.message);
    if (!process.env.GEMINI_API_KEY) {
      console.log('\n💡 提示: 请设置环境变量 GEMINI_API_KEY');
      console.log('   Windows PowerShell: $env:GEMINI_API_KEY="your-api-key"');
      console.log('   或者直接在代码中替换 YOUR_API_KEY_HERE');
    }
  }
}

// 执行测试
testGemini();
