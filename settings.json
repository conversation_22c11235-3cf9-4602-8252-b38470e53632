{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "nova-memory": {"type": "stdio", "command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"], "timeout": 30000, "enabled": true, "env": {"NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data", "NOVA_LOG_LEVEL": "info", "NOVA_CONFIG_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\windows-global-config.js"}, "autoapprove": ["memory", "board", "workflow", "quick", "relationships", "analysis", "project", "settings", "help"]}}}