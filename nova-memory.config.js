/**
 * Nova Memory Configuration for Gemini Project
 * Uses a project-local database to comply with CLI agent security constraints.
 * Modified by Gemini CLI - 2025-08-28
 */

module.exports = {
  // Use project-local configuration
  project: {
    projectPath: 'G:/g-gemini-project', // Changed from global path
    projectName: 'LOCAL_PROJECT_MEMORY' // Changed from GLOBAL_SHARED_MEMORY
  },

  // Use project-local database
  memory: {
    databasePath: 'G:/g-gemini-project/data/memory.db' // Changed from global path
  },

  database: {
    path: 'G:/g-gemini-project/data/memory.db' // Changed from global path
  },

  ai: {
    proactive_search: true,
    entity_detection: true,
    silent_operations: true
  }
};
